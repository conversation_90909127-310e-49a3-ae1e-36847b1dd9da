import { Request, Response } from "express";
import db from "../../../config/db";
import { TABLE } from "../../../utils/Database/table";
import { sendResponse } from "../../../utils/helperFunctions/responseHelper";

export const getDashboardStats = async (req: Request, res: Response) => {
  try {
    // Count doctors
    const [{ count: doctorCount }] = await db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .where(`${TABLE.ROLES}.role_name`, "doctor")
      .count({ count: "*" });

    // Count patients
    const [{ count: patientCount }] = await db(TABLE.PATIENTS).count({ count: "*" });

    // Count employees
    const [{ count: specialistCount }] = await db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .where(`${TABLE.ROLES}.role_name`, "specialist")
      .count({ count: "*" });

    // Count pending patients (status = 'sent_by_doctor')
    const pendingPatientsSubquery = db(TABLE.PATIENTS_VERSIONS)
      .select('patient_id')
      .whereIn('id', function() {
        this.select(db.raw('MAX(id)'))
          .from(TABLE.PATIENTS_VERSIONS)
          .groupBy('patient_id');
      })
      .where('status', 'sent_by_doctor');

    const [{ count: pendingPatientCount }] = await db(TABLE.PATIENTS)
      .whereIn('id', pendingPatientsSubquery)
      .count({ count: "*" });

    // Count incomplete patients (patients with no patient versions)
    const [{ count: incompletePatientCount }] = await db(TABLE.PATIENTS)
      .leftJoin(TABLE.PATIENTS_VERSIONS, `${TABLE.PATIENTS}.id`, "=", `${TABLE.PATIENTS_VERSIONS}.patient_id`)
      .whereNull(`${TABLE.PATIENTS_VERSIONS}.patient_id`)
      .count({ count: `${TABLE.PATIENTS}.id` });

    // Count complete patients (patients with at least one patient version)
    const [{ count: completePatientCount }] = await db(TABLE.PATIENTS)
      .join(TABLE.PATIENTS_VERSIONS, `${TABLE.PATIENTS}.id`, "=", `${TABLE.PATIENTS_VERSIONS}.patient_id`)
      .countDistinct({ count: `${TABLE.PATIENTS}.id` });

    sendResponse(res, 200, "Dashboard stats fetched", true, {
      doctors: Number(doctorCount),
      patients: Number(patientCount),
      specialist: Number(specialistCount),
      pendingPatients: Number(pendingPatientCount),
      incompletePatients: Number(incompletePatientCount),
      completePatients: Number(completePatientCount),
    });
  } catch (error: any) {
    sendResponse(res, 500, error.message, false);
  }
};

export const getDoctorsWithPatientCount = async (req: Request, res: Response) => {
  try {
    const doctorsWithCounts = await db(TABLE.USERS)
      .join(TABLE.ROLES, `${TABLE.USERS}.role_id`, "=", `${TABLE.ROLES}.id`)
      .leftJoin(TABLE.PATIENTS, `${TABLE.PATIENTS}.doctor_id`, "=", `${TABLE.USERS}.id`)
      .where(`${TABLE.ROLES}.role_name`, "doctor")
      .groupBy(`${TABLE.USERS}.id`, `${TABLE.USERS}.first_name`, `${TABLE.USERS}.last_name`)
      .select(
        `${TABLE.USERS}.id as doctor_id`,
        db.raw(`CONCAT(${TABLE.USERS}.first_name, ' ', ${TABLE.USERS}.last_name) as doctor_name`)
      )
      .count<{ doctor_id: number; doctor_name: string; patient_count: number }[]>({
        patient_count: `${TABLE.PATIENTS}.id`
      });

    sendResponse(res, 200, "Doctors with patient count fetched", true, doctorsWithCounts);
  } catch (error: any) {
    sendResponse(res, 500, error.message, false);
  }
};